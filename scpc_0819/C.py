problem_num = int(input())
result = ""

def swap(binary, i, j):
    binary = list(binary)
    binary[i], binary[j] = binary[j], binary[i]
    return ''.join(binary)

for _ in range(problem_num):
    n = int(input())
    binary = int(input())
    len_binary = len(str(binary))
    partial_result = ""
    list_of_possible_decimal_and_swap_num = []
    swap_num = 0
    for i in range(len_binary):
        for j in range(i+1, len_binary):
            binary_swapped = swap(binary, i, j)
            print(binary_swapped)
            swap_num += 1
            decimal = int(binary_swapped, 2)
            list_of_possible_decimal_and_swap_num.append([decimal, swap_num])
    
    for i in range(1, n):
        target = 2**i
        for decimal, swap_num in list_of_possible_decimal_and_swap_num:
            if decimal % target == 0:
                partial_result += str(swap_num) + ' '
                break
        else:
            partial_result += "-1" + ' '
        
    result += partial_result + '\n'
    

print(result.strip())