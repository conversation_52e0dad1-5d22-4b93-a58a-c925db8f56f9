problem_num = int(input())
result = ""

for _ in range(problem_num):
    num_of_chains = input().split()
    num_of_vertices = list(map(int, input().split()))
    arr_a = list(map(int, input().split()))
    arr_b = list(map(int, input().split()))

    length = 0
    for i in range(num_of_chains):
        if arr_a[i] == arr_b[i]:
            length += 2
            # start
            for j in range(i, num_of_chains):
                if arr_a[j] == arr_b[j]:
                    length += 
                    # end




print(result.strip())