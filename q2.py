def IdealCastleLocation(grid, n, r):
    """
    Find an ideal castle location where no higher elevation exists within range r.
    
    Args:
        grid: A 2D list representing the terrain heights (0-indexed)
        n: The size of the grid (n x n)
        r: The range to check for higher elevations (Manhattan distance)
        
    Returns:
        A tuple (i, j) representing the ideal location (1-indexed), or None if no valid location exists
    """
    # Helper function to check if a position is valid
    def is_valid(i, j):
        height = grid[i-1][j-1]
        
        for di in range(-r, r+1):
            for dj in range(-r, r+1):
                if abs(di) + abs(dj) <= r:
                    i_prime = i + di
                    j_prime = j + dj
                    
                    if 1 <= i_prime <= n and 1 <= j_prime <= n:
                        if grid[i_prime-1][j_prime-1] > height:
                            return False
        
        return True
    
    # Track visited regions to prevent infinite recursion
    visited_regions = set()
    
    # Recursive divide-and-conquer function
    def find_ideal_location(row_start, row_end, col_start, col_end):
        # Check if we've already visited this region
        region_key = (row_start, row_end, col_start, col_end)
        if region_key in visited_regions:
            # If we're revisiting a region, just scan it directly
            for i in range(row_start, row_end + 1):
                for j in range(col_start, col_end + 1):
                    if is_valid(i, j):
                        return (i, j)
            return None
        
        # Mark this region as visited
        visited_regions.add(region_key)
        
        # Base case: small enough grid
        if row_end - row_start <= 2*r or col_end - col_start <= 2*r:
            # Scan all tiles in this small grid
            for i in range(row_start, row_end + 1):
                for j in range(col_start, col_end + 1):
                    if is_valid(i, j):
                        return (i, j)
            return None
        
        # Find middle row and column
        mid_row = (row_start + row_end) // 2
        mid_col = (col_start + col_end) // 2
        
        # Examine the central cross
        max_height = float('-inf')
        max_pos = None
        
        # Check middle row
        for j in range(col_start, col_end + 1):
            height = grid[mid_row-1][j-1]
            if height > max_height:
                max_height = height
                max_pos = (mid_row, j)
        
        # Check middle column
        for i in range(row_start, row_end + 1):
            if i == mid_row:  # Already checked in middle row
                continue
            height = grid[i-1][mid_col-1]
            if height > max_height:
                max_height = height
                max_pos = (i, mid_col)
        
        # Check if the maximum position is valid
        if max_pos and is_valid(*max_pos):
            return max_pos
        
        # Find a higher neighbor
        i, j = max_pos
        higher_neighbor = None
        higher_quadrant = None
        
        for di in range(-r, r+1):
            for dj in range(-r, r+1):
                if abs(di) + abs(dj) <= r:
                    i_prime = i + di
                    j_prime = j + dj
                    
                    if 1 <= i_prime <= n and 1 <= j_prime <= n:
                        if grid[i_prime-1][j_prime-1] > grid[i-1][j-1]:
                            higher_neighbor = (i_prime, j_prime)
                            
                            # Determine which quadrant to recurse on
                            if i_prime < mid_row and j_prime < mid_col:
                                higher_quadrant = "top_left"
                            elif i_prime < mid_row and j_prime >= mid_col:
                                higher_quadrant = "top_right"
                            elif i_prime >= mid_row and j_prime < mid_col:
                                higher_quadrant = "bottom_left"
                            else:
                                higher_quadrant = "bottom_right"
                            
                            break
                
                if higher_neighbor:
                    break
            
            if higher_neighbor:
                break
        
        # If no higher neighbor found, scan the entire region
        if not higher_neighbor:
            for i in range(row_start, row_end + 1):
                for j in range(col_start, col_end + 1):
                    if is_valid(i, j):
                        return (i, j)
            return None
        
        # Calculate new boundaries for the quadrant
        if higher_quadrant == "top_left":
            new_row_start = row_start
            new_row_end = mid_row
            new_col_start = col_start
            new_col_end = mid_col
        elif higher_quadrant == "top_right":
            new_row_start = row_start
            new_row_end = mid_row
            new_col_start = mid_col + 1
            new_col_end = col_end
        elif higher_quadrant == "bottom_left":
            new_row_start = mid_row + 1
            new_row_end = row_end
            new_col_start = col_start
            new_col_end = mid_col
        else:  # bottom_right
            new_row_start = mid_row + 1
            new_row_end = row_end
            new_col_start = mid_col + 1
            new_col_end = col_end
        
        # Add buffer of size r
        new_row_start = max(row_start, new_row_start - r)
        new_row_end = min(row_end, new_row_end + r)
        new_col_start = max(col_start, new_col_start - r)
        new_col_end = min(col_end, new_col_end + r)
        
        # Ensure we're making progress
        if (new_row_end - new_row_start) * (new_col_end - new_col_start) >= (row_end - row_start) * (col_end - col_start):
            # If the subproblem isn't smaller, just scan the current region
            for i in range(row_start, row_end + 1):
                for j in range(col_start, col_end + 1):
                    if is_valid(i, j):
                        return (i, j)
            return None
        
        # Recurse on the appropriate quadrant
        result = find_ideal_location(new_row_start, new_row_end, new_col_start, new_col_end)
        
        # If no result found in the chosen quadrant, scan the entire region
        if not result:
            for i in range(row_start, row_end + 1):
                for j in range(col_start, col_end + 1):
                    if is_valid(i, j):
                        return (i, j)
        
        return result
    
    # Start the recursive search
    return find_ideal_location(1, n, 1, n)


# Example usage:
def main():
    # Example grid (10x10)
    grid = [
        [10, 12, 15, 18, 20, 22, 25, 28, 30, 25],
        [12, 15, 18, 20, 22, 25, 28, 30, 32, 28],
        [15, 18, 20, 22, 25, 28, 30, 32, 35, 30],
        [18, 20, 22, 25, 28, 30, 32, 35, 38, 32],
        [20, 22, 25, 28, 30, 32, 35, 38, 40, 35],
        [22, 25, 28, 30, 32, 35, 38, 40, 42, 38],
        [25, 28, 30, 32, 35, 38, 40, 42, 45, 40],
        [28, 30, 32, 35, 38, 40, 42, 45, 48, 42],
        [30, 32, 35, 38, 40, 42, 45, 48, 50, 45],
        [25, 28, 30, 32, 35, 38, 40, 42, 45, 40]
    ]
    n = 10  # 10x10 grid
    r = 3   # Range to check
    
    # Print the grid
    print("Grid:")
    for row in grid:
        print(row)
    
    result = IdealCastleLocation(grid, n, r)
    if result:
        i, j = result
        print(f"Ideal castle location: ({i}, {j}) with height {grid[i-1][j-1]}")
    else:
        print("No valid castle location exists")
    
    # Verify the result
    if result:
        i, j = result
        height = grid[i-1][j-1]
        valid = True
        
        for di in range(-r, r+1):
            for dj in range(-r, r+1):
                if abs(di) + abs(dj) <= r:
                    i_prime = i + di
                    j_prime = j + dj
                    
                    if 1 <= i_prime <= n and 1 <= j_prime <= n:
                        if grid[i_prime-1][j_prime-1] > height:
                            valid = False
                            print(f"Invalid result: Position ({i_prime}, {j_prime}) with height {grid[i_prime-1][j_prime-1]} is higher than ({i}, {j}) with height {height}")
                            break
            
            if not valid:
                break
        
        if valid:
            print("Verification: Result is valid!")


if __name__ == "__main__":
    main()
