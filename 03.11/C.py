problem_num = int(input(""))
result = ""

def countAndMerge(arr, l, m, r):

    n1 = m - l + 1
    n2 = r - m

    left = arr[l:m + 1]
    right = arr[m + 1:r + 1]

    res = 0
    i = 0
    j = 0
    k = l

    while i < n1 and j < n2:
        if left[i] <= right[j]:
            arr[k] = left[i]
            i += 1
        else:
            arr[k] = right[j]
            j += 1
            res += (n1 - i)
        k += 1

    while i < n1:
        arr[k] = left[i]
        i += 1
        k += 1

    while j < n2:
        arr[k] = right[j]
        j += 1
        k += 1

    return res

def countInv(arr, l, r):
    res = 0
    if l < r:
        m = (r + l) // 2

        res += countInv(arr, l, m)
        res += countInv(arr, m + 1, r)

        res += countAndMerge(arr, l, m, r)
    return res

def inversionCount(arr):
    return countInv(arr, 0, len(arr) - 1)

for i in range(0, problem_num*2, 2):
    n = int(input(""))
    arr = list(map(int, input().split()))
    res = 0
    res_pair = [0, 0]


    

    for i in range(n - 1, 0, -1):
        arr_copy = arr
        for j in range(i):
            if arr_copy[j] > arr_copy[j + 1]:
                arr_copy[j], arr_copy[j + 1] = arr_copy[j + 1], arr_copy[j]
                answer = inversionCount(arr_copy)
                if answer < res:
                    res = answer
                    res_pair = [j + 1, j + 2]

                arr_copy[j], arr_copy[j + 1] = arr_copy[j + 1], arr_copy[j]

    result += str(res_pair[0]) + ' ' + str(res_pair[1]) + '\n'

print(result.strip())




        