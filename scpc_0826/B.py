problem_num = int(input())
result = ""

def xor(a, b):
    cost = 0
    if a!=b:
        if a%2==0: #add, then xor, then add, then xor
            a ^= 1
            cost += y
        else:
            a += 1
            cost += x
    return cost

for _ in range(problem_num):
    arr = list(map(int, input().split()))
    a = arr[0]
    b = arr[1]
    x = arr[2]  #cost of a <- a+1
    y = arr[3]  #cost of a <- a^1

    if a == b:
        result += "0\n"
        continue

    if a < b:
        cost_direct = (b - a) * x
        cost=0
        if a%2==0: #add, then xor, then add, then xor
            if a != b:
                a ^= 1
                cost += y
                a += 1
                cost += x
        else: #xor, then add, then xor, then add
            if a != b:
                a += 1
                cost += x
                a ^= 1
                cost += y
        min_cost = min(cost_direct, cost)
        result += str(min_cost) + '\n'
    else:  
        a_xor = a ^ 1
        if a_xor <= b:
            cost = y + (b - a_xor) * x
            result += str(cost) + '\n'
        else:
            result += "-1\n"

print(result.strip())