def is_effective(list_v: list, list_c: list, char: int):
    v_a = 0
    c_a = 0
    j = 0
    k = 0

    while j < len(list_v):
        if list_v[j][char] == "Y":
            v_a += 1
        j += 1

    while k < len(list_c):
        if list_c[k][char] == "Y":
            c_a += 1
        k += 1

    pro_v = v_a / len(list_v)
    pro_c = c_a / len(list_c)

    if v_a >= c_a:
        result = "Not Effective"
    else:
        prob = 1 - pro_v / pro_c
        result = "{:.6f}".format(prob * 100)
    return result

num = int(input())
list_v = []
list_c = []

i = 0
while i < num:
    sample = input()
    if sample[0] == "Y":
        list_v.append(sample)
    else:
        list_c.append(sample)
    i += 1

print(is_effective(list_v, list_c, 1))
print(is_effective(list_v, list_c, 2))
print(is_effective(list_v, list_c, 3))



