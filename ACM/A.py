def solve(n, r, m, tricks):
    dp = [0] * (r + 1)
    for i in range(r - 1, n - 1, -1):
        dp[i] = r - i
    for i in range(n - 1, -1, -1):
        for j in range(m):
            t, p, d = tricks[j]
            if i + t < n:
                dp[i] += p * (d + dp[int(i + t)])
            else:
                dp[i] += p * (d + dp[r])
    return dp[0]

if __name__ == "__main__":
    n, r, m = map(int, input().split())
    tricks = [list(map(float, input().split())) for _ in range(m)]

    result = solve(n, r, m, tricks)
    print("%.10f" % result)