problem_num = int(input(""))
time_list = input("")
time_list = time_list.split(" ")
total = []
for i in range(problem_num):
    time_list[i] = int(time_list[i])
time_list.sort()
penalty = 0
time = 0
total_time = 0
for i in range(problem_num):
  time += time_list[i]
  if time > 300:
    break
  else:
    penalty = penalty + time_list[i]
    total.append(penalty)
for n in total:
    total_time += n
print(len(total),total_time)
