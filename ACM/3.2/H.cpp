#include<bits/stdc++.h>
#include<queue>
using namespace std;
int n;
queue<string> qu[101];
int used[43];
int main(){
	cin>>n;
	for(int i=1;i<=n;i++)
	{
		int id;
		cin>>id;
		if(used[id]==1)
		{
			while(qu[id].size())
				qu[id].pop();
			for(int i=1;i<=id;i++)
			{
				string s;
				cin>>s;
				qu[id].push(s);
			}
		}
		else
		{
			used[id]=1;
			for(int i=1;i<=id;i++)
			{
				string s;
				cin>>s;
				qu[id].push(s);
			}
		}
	}
	int cnt;
	for(int i=1;i<=42;i++)
		if(used[i]==1)
		{
			cnt=i;
			break;
		}
	cout<<cnt<<endl;
	while(qu[cnt].size())
	{
		string s=qu[cnt].front();
		qu[cnt].pop();
		cout<<s<<endl;
	}
    return 0;
}

