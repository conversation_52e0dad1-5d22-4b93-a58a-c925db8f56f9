package tests;

import java.util.Scanner;

public class Number {
    public static void main(String[] args) {
        int input = 0;     //存放输入的整数
        int i = 0, j = 0, k = 0, l = 0, m = 0;//设置五个整数作为数组的下标
        int num = 0;       //统计从0到输入的整数之间的回文数的个数

        Scanner scanner = new Scanner(System.in);
        System.out.println("Input:");
        input = scanner.nextInt();
        int[] arr0 = new int[input];

        for (i = 0; i < input; i++) {     //统计从0到输入的整数之间的回文数,存入数组arr0
            if (isPalindrome(i)) {
                arr0[num] = i;
                num++;
            }
        }

        int[] arr1 = new int[num];    //重新创建五个数组，把前面统计的回文数放入数组
        for (i = 0; i < num; i++)
            arr1[i] = arr0[i];

        boolean b = false;
        for (i = 1; i < num; i++) {              //对五个数组进行遍历，计算五个回文数之和是否等于输入的整数
            for (j = 1; j < num; j++) {
                for (k = 1; k < num; k++) {
                    for (l = 1; l < num; l++) {
                        for (m = 1; m < num; m++) {
                            if (arr1[i] + arr1[j] + arr1[k] + arr1[l] + arr1[m] == input) {
                                System.out.println("Output:");
                                System.out.println(arr1[i]);
                                System.out.println(arr1[j]);
                                System.out.println(arr1[k]);
                                System.out.println(arr1[l]);
                                System.out.println(arr1[m]);
                                b = true;
                                break;         //找到一组符合条件的数就结束循环并输出
                            }
                            if (b) break;
                        }
                        if (b) break;
                    }
                    if (b) break;
                }
                if (b) break;
            }
            if (b) break;
        }
    }

    //判断x是否为回文数
    private static boolean isPalindrome(int x) {
        if (x < 0 || (x % 10 == 0 && x != 0)) {
            return false;
        }

        long revertedNum = 0;
        while (x > revertedNum) {
            revertedNum = revertedNum * 10 + x % 10;
            x /= 10;
        }

        return x == revertedNum || x == revertedNum / 10;
    }
}