#include<bits/stdc++.h>
#include<cmath>
using namespace std;
long long a,b,c;
int flag=0;
int main(){
	cin>>a>>b>>c;
	if(a!=4||b%2!=0)
		cout<<"impossible";
	else
	{
		b=b/2;
		long long p=sqrt(c);
		for(int n=1;n<=p;n++)
		{
			if(c%n==0)
			{
				int m=c/n;
				if(m+n==b)
				{
					cout<<m+2<<" "<<n+2;
					flag=1;
					break;
				} 
			}
		}
		if(flag==0)
			cout<<"impossible";
	}
    return 0;
}

