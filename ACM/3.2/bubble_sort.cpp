#include<bits/stdc++.h>
using namespace std;
int n,p;
int cnt[10001];
int num[10001];
int main(){
	cin>>n>>p;
	int id=p;
	int flag=0;
	for(int i=1;i<=n;i++)
	{
		cin>>num[i];
		if(num[i]!=0)
			flag=1;
	}
	if(flag==0)
	{
		for(int i=1;i<=n;i++)
		{
			cout<<0<<endl;
		}
	}
	else
	{
		cnt[1]=id;
		for(int i=2;i<=n;i++)
		{
			if(num[i]<num[i-1])
			{
				id--;
				cnt[i]=id;
			}
			else
				cnt[i]=id;
		}
		if(id!=0)
		{
			cout<<"ambigous";
		}
		else
		{
			for(int i=1;i<=n;i++)
			{
				cout<<cnt[i]<<endl;
			}
		}
	}
    return 0;
}

