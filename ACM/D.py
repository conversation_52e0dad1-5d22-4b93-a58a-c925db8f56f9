problem_num = int(input())
total_result = ''

for i in range(problem_num):
    test = input().split()
    wine = int(test[0])
    critic = int(test[1])

    red = 0
    white = 0
    for j in range(critic):
        variety = input().split()
        red = max(int(variety[0]), red)
        white = max(int(variety[1]), white)

    if red + white > wine:
        result = "IMPOSSIBLE"
    else:
        result = "R" * red + "W" * white

    total_result += result + '\n'

print(total_result.rstrip())
