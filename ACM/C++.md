# C++

## C++ if, if...else和嵌套if...else

### 条件/三元运算符（？：）

```c++
if ( a < b ) {
   a = b;
}
else {
   a = -b;
}
```

使用三元运算将以上代码替换为：

```c++
a = (a < b) ? b : -b;
```



## C++ while 和 do...while 循环

### C ++ do... while 循环语句

do ... while循环是while循环的变体，但有一个重要区别。do ... while循环的主体在检查测试表达式（testExpression）之前执行一次。

```c++
do {
   // 执行代码;
}
while (testExpression);
```



## C++ break和continue 语句

### C ++ continue语句

#### 示例2：C ++继续

**C ++程序显示1到10之间的整数，除了6和9。**

```
#include <iostream>
using namespace std;

int main()
{
    for (int i = 1; i <= 10; ++i)
    {
        if ( i == 6 || i == 9)
        {
            continue;
        }
        cout << i << "\t";
    }
    return 0;
}
```



## C++ switch 语句

### C ++ switch ...case 语句语法

```c++
switch (n)
{
    case constant1:
        //如果n等于constant1，将执行的代码；
        break;

    case constant2:
        //如果n等于constant2，将执行的代码；
        break;
        .
        .
        .
    default:
        // 如果n不匹配任何constant，将执行的代码
}
```

