package tests;

import java.util.Scanner;

public class F {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        System.out.println("input");
        int n=scanner.nextInt();
        long[] a=new long[n];
        for(int i=0;i<n;i++)
            a[i]=scanner.nextLong();
        while(a.length>1) {  //如果数组里面不是一个数就继续循序
            a=merge(a);      //如果相邻两数有相同的，就合并
            a=delMin(a);   //删除最小的数
            print(a);
        }
        print(a);  //最后的结果
    }

    //如果相邻两数有相同的，就合并
    static long[] merge(long[] a){
        int num=a.length;
        int i=0;
        while(i<a.length-1){
            if(a[i]==a[i+1]){
                a[i]=a[i]*2;
                a=move(a,i);
                i=0;
                print(a);
            }
            else
                i++;
        }
        return a;
    }

    //数组中索引为i+2及之后的数全部往前移动一位,创建新数组
    static long[] move(long[] a,int index){
        for(int i=index;i< (a.length-2);i++)
            a[i+1]=a[i+2];
        long[] b=new long[a.length-1];
        for(int i=0;i< b.length;i++)
            b[i]=a[i];
        return b;
    }

    //删除最小的数
     static long[] delMin(long[] a){
        long min=a[0];
        int index=0;
         for(int i = 0;i<a.length;i++)
             if(min>a[i]) {
                 min = a[i];
                 index=i;
             }
         long[] b=move(a,index-1);
         return b;
     }

    static void print(long[] a){
        for(int i = 0;i<a.length;i++)
            System.out.print(a[i]+" ");
        System.out.println();
    }
}
