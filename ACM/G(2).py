def is_effective(list_v: list, list_c: list, char: int):
    # 计算感染人数
    v_a = sum(1 for item in list_v if item[char] == 'Y')
    c_a = sum(1 for item in list_c if item[char] == 'Y')

    # 计算感染率
    pro_v = v_a / len(list_v)
    pro_c = c_a / len(list_c)

    # 判断有效性并计算疫苗效果
    if pro_v >= pro_c:
        return "Not Effective"
    else:
        efficacy = (1 - pro_v / pro_c) * 100
        return "{:.6f}".format(efficacy)

num = int(input())
list_v = []
list_c = []

i = 0
while i < num:
    sample = input()
    if sample[0] == "Y":
        list_v.append(sample)
    else:
        list_c.append(sample)
    i += 1

print(is_effective(list_v, list_c, 1))
print(is_effective(list_v, list_c, 2))
print(is_effective(list_v, list_c, 3))



