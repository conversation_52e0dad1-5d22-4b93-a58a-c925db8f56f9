import sys

it = iter(sys.stdin.read().strip().split())
t = int(next(it))
ans = []

for _ in range(t):
    n = int(next(it)); m = int(next(it))
    grid = [next(it) for _ in range(n)]

    # Track bounding boxes for W and B
    W = {'rmin': n, 'rmax': -1, 'cmin': m, 'cmax': -1, 'present': False}
    B = {'rmin': n, 'rmax': -1, 'cmin': m, 'cmax': -1, 'present': False}

    for i in range(n):
        row = grid[i]
        for j, ch in enumerate(row):
            if ch == 'W':
                W['present'] = True
                W['rmin'] = min(W['rmin'], i)
                W['rmax'] = max(W['rmax'], i)
                W['cmin'] = min(W['cmin'], j)
                W['cmax'] = max(W['cmax'], j)
            else:
                B['present'] = True
                B['rmin'] = min(B['rmin'], i)
                B['rmax'] = max(B['rmax'], i)
                B['cmin'] = min(B['cmin'], j)
                B['cmax'] = max(B['cmax'], j)

    # If only one color exists, already uniform
    if not W['present'] or not B['present']:
        ans.append("YES")
        continue

    white_full = (W['rmin'] == 0 and W['rmax'] == n-1 and
                  W['cmin'] == 0 and W['cmax'] == m-1)
    black_full = (B['rmin'] == 0 and B['rmax'] == n-1 and
                  B['cmin'] == 0 and B['cmax'] == m-1)

    ans.append("YES" if white_full or black_full else "NO")

print("\n".join(ans))
