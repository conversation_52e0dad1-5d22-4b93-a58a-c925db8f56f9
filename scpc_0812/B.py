problem_num = int(input())
result = ""

# def binary_search(a, x):
#     lo, hi = 0, len(a) - 1
#     while lo <= hi:
#         mid = (lo + hi) // 2
#         if a[mid] == x:
#             return mid
#         elif a[mid] < x:
#             lo = mid + 1
#         else:
#             hi = mid - 1
#     return -1

for i in range(problem_num):
    n, s = map(int, input().split())
    xs = list(map(int, input().split()))
    L, R = xs[0], xs[-1]
    steps = (R - L) + min(abs(s - L), abs(s - R))
    result += str(steps) + '\n'

print(result.strip())