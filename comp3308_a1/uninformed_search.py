from collections import deque
from utils import flip_bit

def bfs(start, legal, unsafe, k):
    """
    1. Cycles should be avoided whenever possible: When selecting the next node to expand from the fringe, if it hasn't been expanded yet than expand it. Otherwise, discard it and select a new node. 
    2. When expanding a node, generate its children in ascending order of the index being flipped. i.e. The first child generated has the 1st bit from the left flipped, and the last child generated has the k-th bit from the left flipped. 
    3. Set a limit of 1000 expanded nodes maximum.
    """
    # Edge case: check if start is unsafe or legal is empty
    if start in unsafe:
        return None, []
    if not legal:
        return None, []
    if k < 3 or k > 12:  # Valid range for k
        return None, []
    
    # Edge case: if start is already a legal state
    if start in legal:
        return [start], [start]

    queue = deque([start])
    visited = set([start])
    parent = {}
    parent[start] = None
    expanded_nodes = []
    expanded_nodes_count = 0
    max_expanded_nodes = 1000

    while queue and expanded_nodes_count < max_expanded_nodes: 
        current = queue.popleft()
        expanded_nodes.append(current)
        expanded_nodes_count += 1

        # Check if reached a legal state
        if current in legal:
            path = []
            node = current
            while node is not None:
                path.append(node)
                node = parent[node]
            path.reverse()
            return path, expanded_nodes
        
        # Generate children
        for i in range(k):
            child = flip_bit(current, i)
            if child not in visited and child not in unsafe:
                visited.add(child)
                parent[child] = current
                queue.append(child)
    
    
    return None, expanded_nodes


def dfs(start, legal, unsafe, k):
    """
    Depth-First Search
    """
    # Edge case: check if start is unsafe or legal is empty
    if start in unsafe:
        return None, []
    if not legal:
        return None, []
    if k < 3 or k > 12:  # Valid range for k
        return None, []
    
    # Edge case: if start is already a legal state
    if start in legal:
        return [start], [start]
        
    visited = set()
    expanded_nodes = []
    expanded_nodes_count = 0
    max_expanded_nodes = 1000
    
    def dfs_recursive(current):
        nonlocal expanded_nodes_count
        
        # Check limit
        if expanded_nodes_count >= max_expanded_nodes:
            return None
            
        if current in visited:
            return None
            
        visited.add(current)
        expanded_nodes.append(current)
        expanded_nodes_count += 1
        
        # Check if reached a legal state
        if current in legal:
            return [current]
            
        # Generate children
        for i in range(k):
            next_state = flip_bit(current, i)
            if next_state not in unsafe and next_state not in visited:
                result = dfs_recursive(next_state)
                if result:
                    return [current] + result
        return None
    
    
    try:
        path = dfs_recursive(start)
        if path:
            return path, expanded_nodes
        else:
            return None, expanded_nodes
    except RecursionError:
        return None, expanded_nodes


def ids(start, legal, unsafe, k):
    """
    Iterative Deepening Search
    """
    # Edge case: check if start is unsafe or legal is empty
    if start in unsafe:
        return None, []
    if not legal:
        return None, []
    if k < 3 or k > 12:  # Valid range for k
        return None, []
    
    # Edge case: if start is already a legal state
    if start in legal:
        return [start], [start]
        

    max_depth = 2**k if k <= 10 else 1000
    all_expanded_nodes = []
    total_expanded_count = 0
    max_total_expanded = 1000
    
    for depth_limit in range(max_depth):
        if total_expanded_count >= max_total_expanded:
            break
            
        visited = set()
        expanded_nodes = []
        
        def dls(current, depth):
            nonlocal total_expanded_count
            
            if total_expanded_count >= max_total_expanded:
                return None
                
            if current in visited:
                return None
                
            visited.add(current)
            expanded_nodes.append(current)
            total_expanded_count += 1

            # Check if reached a legal state
            if current in legal:
                return [current]
                
            # Check if reached current depth limit
            if depth >= depth_limit:
                return None
                
            # Generate children
            for i in range(k):
                next_state = flip_bit(current, i)
                if next_state not in unsafe and next_state not in visited:
                    result = dls(next_state, depth + 1)
                    if result:
                        return [current] + result
            return None
        
        try:
            path = dls(start, 0)
            all_expanded_nodes.extend(expanded_nodes)
            
            if path:
                return path, all_expanded_nodes
        except RecursionError:
            all_expanded_nodes.extend(expanded_nodes)
            continue
    
    return None, all_expanded_nodes
