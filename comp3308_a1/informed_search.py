import heapq
from utils import flip_bit, hamming_distance, heuristic

def greedy(start, legal, unsafe, k):
    """
    Use the smallest Hamming distance from any LEGAL colour for the heuristic in Greedy search.
    If two or more nodes have the same priority for expansion, expand the oldest of those nodes. 
    """
    counter = 0 # older nodes (with a smaller counter) are expanded first
    visited = set([start])
    parent = {}
    parent[start] = None

    expanded_nodes = []
    expanded_nodes_count = 0
    max_expanded_nodes = 1000

    pq = [(heuristic(start, legal), counter, start)]

    while pq:
        h_val, _, current = heapq.heappop(pq)
        expanded_nodes.append(current)
        expanded_nodes_count += 1

        # Check limit
        if expanded_nodes_count > max_expanded_nodes:
            return None

        # Check if reached a legal state
        if current in legal:
            path = []
            node = current
            while node is not None:
                path.append(node)
                node = parent[node]
            path.reverse()
            return path, expanded_nodes

        # Generate children
        for i in range(k):
            child = flip_bit(current, i)
            # print(f"Expand {current}, flip bit {i}, get child = {child}")
            if child not in visited and child not in unsafe:
                visited.add(child)
                # print('child')
                parent[child] = current
                # if same priority for expansion, expand the oldest
                counter += 1 
                heapq.heappush(pq, (heuristic(child, legal), counter, child))

    return None, expanded_nodes    
        



def a_star(start, legal, unsafe, k):
    """
    Use the smallest Hamming distance from any LEGAL colour for the heuristic in A* search.
    Assume each step costs 1.
    If two or more nodes have the same priority (same f-value (g + heuristic)) for expansion, expand the oldest of those nodes. 
    """
    counter = 0
    visited = set()
    g_value = {} # cost from start to the current node
    g_value[start] = 0
    parent = {}
    parent[start] = None
    expanded_nodes = []
    expanded_nodes_count = 0
    max_expanded_nodes = 1000

    start_h = heuristic(start, legal)
    pq = [(start_h, counter, start, 0)]

    while pq:
        f, _, current, g = heapq.heappop(pq)
        
        # Skip if this node has already been expanded.
        if current in visited:
            continue
        visited.add(current)
        expanded_nodes.append(current)
        expanded_nodes_count += 1

        # Check limit
        if expanded_nodes_count > max_expanded_nodes:
            return None, expanded_nodes

        # Check if reached a legal state
        if current in legal:
            path = []
            node = current
            while node is not None:
                path.append(node)
                node = parent[node]
            path.reverse()
            return path, expanded_nodes
        
        # Generate children
        for i in range(k):
            child = flip_bit(current, i)
            if child in unsafe:
                continue
            
            tentative_g = g + 1
            # If the child has already been expanded with a lower or equal cost, skip it.
            if child in visited and tentative_g >= g_value.get(child, float('inf')):
                continue
            
            # If this path to child is better than any previous one, record it.
            if tentative_g < g_value.get(child, float('inf')):
                g_value[child] = tentative_g
                parent[child] = current
                counter += 1
                f_child = tentative_g + heuristic(child, legal)
                heapq.heappush(pq, (f_child, counter, child, tentative_g))
    
    return None, expanded_nodes


def hill_climbing(start, legal, unsafe, k):
    """
    Use the smallest Hamming distance from any LEGAL colour as an evaluation function in hill-climbing algorithms. 
    If two or more nodes have the same priority for expansion, expand the oldest of those nodes. 
    """
    current = start
    expanded_nodes = [current]
    expanded_nodes_count = 1
    max_expanded_nodes = 1000

    while expanded_nodes_count < max_expanded_nodes:
        # Check if reached a legal state
        if current in legal:
            return expanded_nodes, expanded_nodes

        current_h = heuristic(current, legal)
        best_neighbor = None

        # Generate neighbour
        for i in range(k):
            neighbor = flip_bit(current, i)
            if neighbor in unsafe:
                continue
            neighbor_h = heuristic(neighbor, legal)
            # look for a neighbor with a strictly lower heuristic value
            if neighbor_h < current_h:
                best_neighbor = neighbor
                break
        
        if best_neighbor is None:
            break
        
        current = best_neighbor
        expanded_nodes.append(current)
        expanded_nodes_count += 1
    
    if current in legal:
        return expanded_nodes, expanded_nodes
    else:
        return None, expanded_nodes


