def parse_input(filename):
    """
    1. Assume that all input files are valid input files.
    2. The program should also be able to automatically determine k, the bit-depth of the colour from the inputs.
    """
    with open(filename, 'r') as f:
        lines = [line.strip() for line in f]
    
    # start_state
    start_state = lines[0]

    # legal_states
    legal_states = []

    if len(lines) > 1:
        legal_line = lines[1]
    else:
        legal_line = ''
    
    if legal_line:
        for state_str in legal_line.split(','):
            legal_states.extend(deal_with_x(state_str.strip()))
    
    # unsafe_states
    unsafe_states = []

    if len(lines) > 2:
        unsafe_line = lines[2]
    else:
        unsafe_line = ''

    if unsafe_line:
        for state_str in unsafe_line.split(','):
            unsafe_states.extend(deal_with_x(state_str.strip()))

    # k, the bit-depth of the colour
    # k can be any integer from 3 to 12 inclusive
    k = len(start_state)

    return start_state, set(legal_states), set(unsafe_states), k


def deal_with_x(state_str):
    """
    1. The X denotes that both 111 and 110 are in UNSAFE.
    2. The X character can only appear in legal-state and unsafe-state inputs.
    3. Additionally, more than one X may be present in an input. 
    """
    if 'X' not in state_str:
        return [state_str]
    
    results = []
    i = state_str.index('X')
    str_before = state_str[:i]
    str_after = state_str[i+1:]
    for bit in ['0', '1']:
        results.extend(deal_with_x(str_before + bit + str_after))
    return results

