import sys
from input_parser import parse_input
from uninformed_search import bfs, dfs, ids
from informed_search import greedy, a_star, hill_climbing

def broken_printer(char, filename):
    """
    Input:
    - <char> Denotes what search strategy to run. B for BFS, D for DFS, I for IDS, G for Greedy, A for A* and H for hill-climbing. 
    - <filename> Refers to the .txt file that describes the start state, LEGAL states and UNSAFE states. 

    Output:
    - Line 1: This line should express the path found to the solution, beginning with the start state and finishing at a goal state.
    - Line 2: This line should express the order in which nodes are expanded in the search process. 
    """
    start_state, legal_states, unsafe_states, k = parse_input(filename)

    if char == 'B':
        path, expanded_nodes = bfs(start_state, legal_states, unsafe_states, k)
    elif char == 'D':
        path, expanded_nodes = dfs(start_state, legal_states, unsafe_states, k) 
    elif char == 'I':
        path, expanded_nodes = ids(start_state, legal_states, unsafe_states, k)
    elif char == 'G':
        path, expanded_nodes = greedy(start_state, legal_states, unsafe_states, k)
    elif char == 'A':
        path, expanded_nodes = a_star(start_state, legal_states, unsafe_states, k)
    elif char == 'H':
        path, expanded_nodes = hill_climbing(start_state, legal_states, unsafe_states, k)
    else:
        return 'SEARCH FAILED'
    
    if path is None:
        return f"SEARCH FAILED\n{','.join(expanded_nodes)}"
    else:
        line_1 = ",".join(path)
        line_2 = ",".join(expanded_nodes)
        return f"{line_1}\n{line_2}"

if __name__ == '__main__':
    if len(sys.argv) < 3:
        # You can modify these values to test your code
        # char = 'H'
        # filename = 'example2.txt'
        char = 'D'
        filename = 'debug_dfs.txt'
        # char = 'I'
        # filename = 'debug_ids.txt'
    else:
        char = sys.argv[1]
        filename = sys.argv[2]
    print(broken_printer(char, filename))
