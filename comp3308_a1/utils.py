def flip_bit(s, i):
    """
    Within each step, we may flip one binary digit of the current colour
    """
    str_before = s[:i]
    str_after = s[i+1:]

    if s[i] == '0':
        bit = '1'
    else:
        bit = '0'
    
    return str_before + bit + str_after
        
def hamming_distance(s1, s2):
    """
    Use the smallest Hamming distance from any LEGAL colour for the heuristic in Greedy and A* search, and as an evaluation function in hill-climbing algorithms. 
    """
    count = 0
    for i in range(len(s1)):
        if s1[i] != s2[i]:
            count += 1
    return count

def heuristic(state, legal_states):
    """
    Calculates the Hamming distance between the current state and all legal goal states, and then selects the smallest value.
    """
    min_distance = None

    for goal in legal_states:
        current_distance = hamming_distance(state, goal)

        if min_distance is None:
            min_distance = current_distance
        else:
            if current_distance < min_distance:
                min_distance = current_distance
        
    if min_distance is None:
        return 0
    else:
        return min_distance



