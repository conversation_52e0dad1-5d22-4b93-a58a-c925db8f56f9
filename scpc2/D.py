problem = input("").split()
n = int(problem[0])
m = int(problem[1])
result = ""

dict = {"a": 2, "b": 2, "c": 2, "d": 3, "e": 3, "f": 3, "g": 4, "h": 4, "i": 4, 
        "j": 5, "k": 5, "l": 5, "m": 6, "n": 6, "o": 6, "p": 7, "q": 7, "r": 7, 
        "s": 7, "t": 8, "u": 8, "v": 8, "w": 9, "x": 9, "y": 9, "z": 9}

phone_dict = {}

for i in range(n):
    word = input("").strip().lower()  
    word_to_num = ""
    for char in word:
        word_to_num += str(dict[char])
  
    if word_to_num not in phone_dict:
        phone_dict[word_to_num] = []
    phone_dict[word_to_num].append(word)

for i in range(m):
    phone = input("").strip()
    if phone in phone_dict:
        words = phone_dict[phone]
        result += str(len(words)) + " " + " ".join(words) + "\n"
    else:
        result += "0\n"

print(result.strip())


    
        
            


    