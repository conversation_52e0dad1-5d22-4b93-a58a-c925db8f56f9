problem = input("").split()
n = int(problem[0])
m = int(problem[1])

dict = {"a": 2, "b": 2, "c": 2, "d": 3, "e": 3, "f": 3, "g": 4, "h": 4, "i": 4,
        "j": 5, "k": 5, "l": 5, "m": 6, "n": 6, "o": 6, "p": 7, "q": 7, "r": 7,
        "s": 7, "t": 8, "u": 8, "v": 8, "w": 9, "x": 9, "y": 9, "z": 9}

word_ls = []
phone_ls = []


def matches(word, phone):
    if len(word) != len(phone):
        return False
    for i in range(len(word)):
        if dict[word[i]] != int(phone[i]):
            return False
    return True


for i in range(n):
    word = input("").strip().lower()
    word_ls.append(word)

for i in range(m):
    phone = input("").strip()
    phone_ls.append(phone)


result = []
for phone in phone_ls:
    matched_words = [word for word in word_ls if matches(word, phone)]
    if matched_words:
        result.append(f"{len(matched_words)} " + " ".join(matched_words))
    else:
        result.append("0")

print("\n".join(result))
