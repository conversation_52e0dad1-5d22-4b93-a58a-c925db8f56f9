n, k, p = map(int, input().split())
result = ""

dict = {"con": "continue", "back": "backspace", "re": "restart"}

p = p / 100

con = (1-p) * ((n-k)*0.1 + 0.1) + p * (((n-k)*0.1 + 0.1) + 0.3 + n*0.1 + 0.1)
#print(con)
back = p * (0.1 + (n-k)*0.1 + 0.1) + (1-p) * ((n-k)*0.1 + 0.1 + 0.1 + 0.3 + n*0.1 + 0.1)
#print(back)
re = 0.3 + 0.1*n + 0.1
#print(re)

if con < back and con < re:
    result += dict["con"]
elif back < con and back < re:
    result += dict["back"]
else:
    result += dict["re"]

print(result.strip())
