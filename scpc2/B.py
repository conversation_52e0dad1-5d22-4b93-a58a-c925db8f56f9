problem_num = int(input(""))
result = ""

for i in range(problem_num):
    n, a, b, c = map(int, input().split())
    workout_list = [a, b, c]
    
    cycle_sum = sum(workout_list)
    complete_cycles = n // cycle_sum
    days = complete_cycles * 3
    remaining = n - (cycle_sum * complete_cycles)
    
    if remaining > 0:
        for weight in workout_list:
            if remaining <= 0:
                break
            remaining -= weight
            days += 1
    
    result += str(days) + '\n'

print(result.strip())
