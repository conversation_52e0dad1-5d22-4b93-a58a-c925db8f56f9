problem_num = int(input(""))
result = ""

for i in range(problem_num):
    m, a, b, c = map(int, input().split())
    current_max = 0
    
    if m - a > 0 and m - b > 0:
        res = m*2 - a - b
        if res > c:
            current_max = a + b + c
        else:
            current_max = a + b + res
    elif m - a > 0 and m - b <= 0:
        res = m - a
        if res > c:
            current_max = a + m + c
        else:
            current_max = a + m + res
    elif m - a <= 0 and m - b > 0:
        res = m - b
        if res > c:
            current_max = m + b + c
        else:
            current_max = m + b + res
    else:
        current_max = m*2
        
    result += str(current_max) + '\n'

print(result.strip())
