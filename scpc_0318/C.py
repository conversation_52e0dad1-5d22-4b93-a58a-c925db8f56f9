problem_num = int(input(""))
result = ""



for i in range(0, problem_num*2, 2):
    n = int(input(""))
    arr = list(map(int, input().split()))
    min_dist = 0
    mid = n/2
    mid_num = arr[mid]
    
    for i in range(mid, 0, -1):
        if arr[mid-1] == arr[mid]:
            min_dist += 1
        if arr[mid+1] == arr[mid]:
            min_dist += 1
        if arr[i] == arr[i-1]:
            pass
    


    result += str(min_dist) + '\n'
    

print(result.strip())
